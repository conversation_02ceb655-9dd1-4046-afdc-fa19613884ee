{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/index.ts"], "sourcesContent": ["import {\n  AppBundlePathNormalizer,\n  DevAppBundlePathNormalizer,\n} from './app-bundle-path-normalizer'\nimport { AppFilenameNormalizer } from './app-filename-normalizer'\nimport { DevAppPageNormalizer } from './app-page-normalizer'\nimport {\n  App<PERSON><PERSON><PERSON><PERSON><PERSON>ali<PERSON>,\n  DevAppPathnameNormalizer,\n} from './app-pathname-normalizer'\n\nexport class AppNormalizers {\n  public readonly filename: AppFilenameNormalizer\n  public readonly pathname: AppPathnameNormalizer\n  public readonly bundlePath: AppBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new AppFilenameNormalizer(distDir)\n    this.pathname = new AppPathnameNormalizer()\n    this.bundlePath = new AppBundlePathNormalizer()\n  }\n}\n\nexport class DevAppNormalizers {\n  public readonly page: DevAppPageNormalizer\n  public readonly pathname: DevAppPathnameNormalizer\n  public readonly bundlePath: DevAppBundlePathNormalizer\n\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    this.page = new DevAppPageNormalizer(appDir, extensions)\n    this.pathname = new DevAppPathnameNormalizer(this.page)\n    this.bundlePath = new DevAppBundlePathNormalizer(this.page)\n  }\n}\n"], "names": ["AppNormalizers", "DevAppNormalizers", "constructor", "distDir", "filename", "AppFilenameNormalizer", "pathname", "AppPathnameNormalizer", "bundlePath", "AppBundlePathNormalizer", "appDir", "extensions", "page", "DevAppPageNormalizer", "DevAppPathnameNormalizer", "DevAppBundlePathNormalizer"], "mappings": ";;;;;;;;;;;;;;;IAWaA,cAAc;eAAdA;;IAYAC,iBAAiB;eAAjBA;;;yCApBN;uCAC+B;mCACD;uCAI9B;AAEA,MAAMD;IAKXE,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIC,4CAAqB,CAACF;QAC1C,IAAI,CAACG,QAAQ,GAAG,IAAIC,4CAAqB;QACzC,IAAI,CAACC,UAAU,GAAG,IAAIC,gDAAuB;IAC/C;AACF;AAEO,MAAMR;IAKXC,YAAYQ,MAAc,EAAEC,UAAiC,CAAE;QAC7D,IAAI,CAACC,IAAI,GAAG,IAAIC,uCAAoB,CAACH,QAAQC;QAC7C,IAAI,CAACL,QAAQ,GAAG,IAAIQ,+CAAwB,CAAC,IAAI,CAACF,IAAI;QACtD,IAAI,CAACJ,UAAU,GAAG,IAAIO,mDAA0B,CAAC,IAAI,CAACH,IAAI;IAC5D;AACF"}