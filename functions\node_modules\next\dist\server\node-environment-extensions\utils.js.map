{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender') {\n      const prerenderSignal = workUnitStore.controller.signal\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct any stacks\n        // because something else actually terminated the prerender.\n        const workStore = workAsyncStorage.getStore()\n        if (workStore) {\n          let message: string\n          switch (type) {\n            case 'time':\n              message = `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n              break\n            case 'random':\n              message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n              break\n            case 'crypto':\n              message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n              break\n            default:\n              throw new InvariantError(\n                'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n              )\n          }\n          const errorWithStack = new Error(message)\n\n          abortOnSynchronousPlatformIOAccess(\n            workStore.route,\n            expression,\n            errorWithStack,\n            workUnitStore\n          )\n        }\n      }\n    } else if (\n      workUnitStore.type === 'request' &&\n      workUnitStore.prerenderPhase === true\n    ) {\n      const requestStore = workUnitStore\n      trackSynchronousPlatformIOAccessInDev(requestStore)\n    }\n  }\n}\n"], "names": ["io", "expression", "type", "workUnitStore", "workUnitAsyncStorage", "getStore", "prerenderSignal", "controller", "signal", "aborted", "workStore", "workAsyncStorage", "message", "route", "InvariantError", "errorWithStack", "Error", "abortOnSynchronousPlatformIOAccess", "prerenderPhase", "requestStore", "trackSynchronousPlatformIOAccessInDev"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;0CAViB;8CACI;kCAI9B;gCACwB;AAIxB,SAASA,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,IAAIA,cAAcD,IAAI,KAAK,aAAa;YACtC,MAAMI,kBAAkBH,cAAcI,UAAU,CAACC,MAAM;YACvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;gBACrC,mFAAmF;gBACnF,4DAA4D;gBAC5D,MAAMC,YAAYC,0CAAgB,CAACN,QAAQ;gBAC3C,IAAIK,WAAW;oBACb,IAAIE;oBACJ,OAAQV;wBACN,KAAK;4BACHU,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAEZ,WAAW,mLAAmL,CAAC;4BAC5O;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAEZ,WAAW,wKAAwK,CAAC;4BACjO;wBACF,KAAK;4BACHW,UAAU,CAAC,OAAO,EAAEF,UAAUG,KAAK,CAAC,OAAO,EAAEZ,WAAW,wKAAwK,CAAC;4BACjO;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIa,8BAAc,CACtB,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBACA,MAAMC,iBAAiB,qBAAkB,CAAlB,IAAIC,MAAMJ,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAExCK,IAAAA,oDAAkC,EAChCP,UAAUG,KAAK,EACfZ,YACAc,gBACAZ;gBAEJ;YACF;QACF,OAAO,IACLA,cAAcD,IAAI,KAAK,aACvBC,cAAce,cAAc,KAAK,MACjC;YACA,MAAMC,eAAehB;YACrBiB,IAAAA,uDAAqC,EAACD;QACxC;IACF;AACF"}