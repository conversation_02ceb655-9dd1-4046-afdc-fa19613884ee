{"version": 3, "sources": ["../../../src/server/lib/app-dir-module.ts"], "sourcesContent": ["import type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport { DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\n\n/**\n * LoaderTree is generated in next-app-loader.\n */\nexport type LoaderTree = [\n  segment: string,\n  parallelRoutes: { [parallelRouterKey: string]: LoaderTree },\n  modules: AppDirModules,\n]\n\nexport async function getLayoutOrPageModule(loaderTree: LoaderTree) {\n  const { layout, page, defaultPage } = loaderTree[2]\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const isDefaultPage =\n    typeof defaultPage !== 'undefined' && loaderTree[0] === DEFAULT_SEGMENT_KEY\n\n  let mod = undefined\n  let modType: 'layout' | 'page' | undefined = undefined\n  let filePath = undefined\n\n  if (isLayout) {\n    mod = await layout[0]()\n    modType = 'layout'\n    filePath = layout[1]\n  } else if (isPage) {\n    mod = await page[0]()\n    modType = 'page'\n    filePath = page[1]\n  } else if (isDefaultPage) {\n    mod = await defaultPage[0]()\n    modType = 'page'\n    filePath = defaultPage[1]\n  }\n\n  return { mod, modType, filePath }\n}\n\nexport async function getComponentTypeModule(\n  loaderTree: LoaderTree,\n  moduleType: 'layout' | 'not-found' | 'forbidden' | 'unauthorized'\n) {\n  const { [moduleType]: module } = loaderTree[2]\n  if (typeof module !== 'undefined') {\n    return await module[0]()\n  }\n  return undefined\n}\n"], "names": ["getComponentTypeModule", "getLayoutOrPageModule", "loaderTree", "layout", "page", "defaultPage", "isLayout", "isPage", "isDefaultPage", "DEFAULT_SEGMENT_KEY", "mod", "undefined", "modType", "filePath", "moduleType", "module"], "mappings": ";;;;;;;;;;;;;;;IAwCsBA,sBAAsB;eAAtBA;;IA5BAC,qBAAqB;eAArBA;;;yBAXc;AAW7B,eAAeA,sBAAsBC,UAAsB;IAChE,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,GAAGH,UAAU,CAAC,EAAE;IACnD,MAAMI,WAAW,OAAOH,WAAW;IACnC,MAAMI,SAAS,OAAOH,SAAS;IAC/B,MAAMI,gBACJ,OAAOH,gBAAgB,eAAeH,UAAU,CAAC,EAAE,KAAKO,4BAAmB;IAE7E,IAAIC,MAAMC;IACV,IAAIC,UAAyCD;IAC7C,IAAIE,WAAWF;IAEf,IAAIL,UAAU;QACZI,MAAM,MAAMP,MAAM,CAAC,EAAE;QACrBS,UAAU;QACVC,WAAWV,MAAM,CAAC,EAAE;IACtB,OAAO,IAAII,QAAQ;QACjBG,MAAM,MAAMN,IAAI,CAAC,EAAE;QACnBQ,UAAU;QACVC,WAAWT,IAAI,CAAC,EAAE;IACpB,OAAO,IAAII,eAAe;QACxBE,MAAM,MAAML,WAAW,CAAC,EAAE;QAC1BO,UAAU;QACVC,WAAWR,WAAW,CAAC,EAAE;IAC3B;IAEA,OAAO;QAAEK;QAAKE;QAASC;IAAS;AAClC;AAEO,eAAeb,uBACpBE,UAAsB,EACtBY,UAAiE;IAEjE,MAAM,EAAE,CAACA,WAAW,EAAEC,OAAM,EAAE,GAAGb,UAAU,CAAC,EAAE;IAC9C,IAAI,OAAOa,YAAW,aAAa;QACjC,OAAO,MAAMA,OAAM,CAAC,EAAE;IACxB;IACA,OAAOJ;AACT"}