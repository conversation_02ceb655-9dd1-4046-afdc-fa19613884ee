{"version": 3, "sources": ["../../../src/server/lib/async-callback-set.ts"], "sourcesContent": ["export class AsyncCallbackSet {\n  private callbacks: (() => Promise<void>)[] = []\n\n  public add(callback: () => Promise<void>) {\n    this.callbacks.push(callback)\n  }\n\n  public async runAll(): Promise<void> {\n    if (!this.callbacks.length) {\n      return\n    }\n    const callbacks = this.callbacks\n    this.callbacks = []\n    await Promise.allSettled(\n      callbacks.map(\n        // NOTE: wrapped in an async function to protect against synchronous exceptions\n        async (f) => f()\n      )\n    )\n  }\n}\n"], "names": ["AsyncCallbackSet", "add", "callback", "callbacks", "push", "runAll", "length", "Promise", "allSettled", "map", "f"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA;IAGJC,IAAIC,QAA6B,EAAE;QACxC,IAAI,CAACC,SAAS,CAACC,IAAI,CAACF;IACtB;IAEA,MAAaG,SAAwB;QACnC,IAAI,CAAC,IAAI,CAACF,SAAS,CAACG,MAAM,EAAE;YAC1B;QACF;QACA,MAAMH,YAAY,IAAI,CAACA,SAAS;QAChC,IAAI,CAACA,SAAS,GAAG,EAAE;QACnB,MAAMI,QAAQC,UAAU,CACtBL,UAAUM,GAAG,CACX,+EAA+E;QAC/E,OAAOC,IAAMA;IAGnB;;aAlBQP,YAAqC,EAAE;;AAmBjD"}