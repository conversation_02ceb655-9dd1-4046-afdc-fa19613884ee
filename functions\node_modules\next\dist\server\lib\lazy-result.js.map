{"version": 3, "sources": ["../../../src/server/lib/lazy-result.ts"], "sourcesContent": ["export type LazyResult<TValue> = PromiseLike<TValue> & { value?: TValue }\nexport type ResolvedLazyResult<TValue> = PromiseLike<TValue> & { value: TValue }\n\n/**\n * Calls the given async function only when the returned promise-like object is\n * awaited. Afterwards, it provides the resolved value synchronously as `value`\n * property.\n */\nexport function createLazyResult<TValue>(\n  fn: () => Promise<TValue>\n): LazyResult<TValue> {\n  let pendingResult: Promise<TValue> | undefined\n\n  const result: LazyResult<TValue> = {\n    then(onfulfilled, onrejected) {\n      if (!pendingResult) {\n        pendingResult = fn()\n      }\n\n      pendingResult\n        .then((value) => {\n          result.value = value\n        })\n        .catch(() => {\n          // The externally awaited result will be rejected via `onrejected`. We\n          // don't need to handle it here. But we do want to avoid an unhandled\n          // rejection.\n        })\n\n      return pendingResult.then(onfulfilled, onrejected)\n    },\n  }\n\n  return result\n}\n\nexport function isResolvedLazyResult<TValue>(\n  result: LazyResult<TValue>\n): result is ResolvedLazyResult<TValue> {\n  return result.hasOwnProperty('value')\n}\n"], "names": ["createLazyResult", "isResolvedLazyResult", "fn", "pendingResult", "result", "then", "onfulfilled", "onrejected", "value", "catch", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;IAQgBA,gBAAgB;eAAhBA;;IA4BAC,oBAAoB;eAApBA;;;AA5BT,SAASD,iBACdE,EAAyB;IAEzB,IAAIC;IAEJ,MAAMC,SAA6B;QACjCC,MAAKC,WAAW,EAAEC,UAAU;YAC1B,IAAI,CAACJ,eAAe;gBAClBA,gBAAgBD;YAClB;YAEAC,cACGE,IAAI,CAAC,CAACG;gBACLJ,OAAOI,KAAK,GAAGA;YACjB,GACCC,KAAK,CAAC;YACL,sEAAsE;YACtE,qEAAqE;YACrE,aAAa;YACf;YAEF,OAAON,cAAcE,IAAI,CAACC,aAAaC;QACzC;IACF;IAEA,OAAOH;AACT;AAEO,SAASH,qBACdG,MAA0B;IAE1B,OAAOA,OAAOM,cAAc,CAAC;AAC/B"}